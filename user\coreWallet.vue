
<template>
	<view class="page">
		<view class="header-stats">
			<view class="stats-card">
				<view class="stats-title">已到账总额</view>
				<view class="stats-amount">￥{{ totalAmount }}</view>
			</view>
		</view>

		<view class="filter-section">
			<u-scroll-list :indicator="true" indicator-color="#f2f2f2" indicator-active-color="#2e80fe">
				<view
					class="filter-tab"
					:class="{ active: currentStatus === item.value }"
					v-for="item in statusOptions"
					:key="item.value"
					@tap="changeStatus(item.value)"
				>
					{{ item.label }}
				</view>
			</u-scroll-list>
		</view>
		<view class="record-list">
			<view
				class="record-item"
				v-for="item in recordList"
				:key="item.id"
				@tap="handleRecordTap(item)"
			>
				<view class="record-header">
					<view class="record-amount">￥{{ item.amount }}</view>
					<view
						class="record-status"
						:class="[getStatusClass(item)]"
						@tap.stop="handleStatusTap(item)"
					>
						{{ item.statusText }}
						<text v-if="item.lock === 1" class="tap-hint">点击提现</text>
						<text v-if="item.lock === 0" class="tap-hint">点击取消</text>
					</view>
				</view>
				<view class="record-info">
					<view class="record-time">{{ item.createTime }}</view>
				</view>
			</view>
		</view>

		<view class="empty-state" v-if="recordList.length === 0 && !loading">
			<!-- <image src="/static/mine/default_user.png" class="empty-image"></image> -->
			<text class="empty-text">暂无提现记录</text>
		</view>

		<view class="load-more" v-if="recordList.length > 0">
			<text class="load-text" v-if="hasMore && !loading">上拉加载更多</text>
			<text class="load-text" v-if="loading">加载中...</text>
			<text class="load-text" v-if="!hasMore && !loading">没有更多数据了</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				totalAmount: 0, // 已到账总额
				currentStatus: '', // 当前选中的状态
				recordList: [], // 提现记录列表
				loading: false, // 加载状态
				hasMore: true, // 是否还有更多数据
				pageNum: 1, // 当前页码
				pageSize: 10, // 每页数量
				statusOptions: [
					{ label: '全部', value: '' },
					{ label: '未提现', value: 0 },
					{ label: '已提现未领取', value: 1 },
					{ label: '已到账', value: 2 },
					{ label: '失败', value: 3 },
					{ label: '关闭', value: 4 }
				]
			}
		},
		onLoad() {
			this.initData();
		},
		onPullDownRefresh() {
			this.refreshData();
		},
		onReachBottom() {
			this.loadMore();
		},
		methods: {
			// 初始化数据
			async initData() {
				await this.getTotalAmount();
				await this.getRecordList(true);
			},

			// 刷新数据
			async refreshData() {
				this.pageNum = 1;
				this.hasMore = true;
				await this.getTotalAmount();
				await this.getRecordList(true);
				uni.stopPullDownRefresh();
			},

			// 获取已到账总额
			async getTotalAmount() {
				try {
					const res = await this.$api.mine.walletStatSimple();
					if (res.code === '200') {
						this.totalAmount = res.data || 0;
					}
				} catch (error) {
					console.error('获取总额失败:', error);
				}
			},

			// 获取提现记录列表
			async getRecordList(reset = false) {
				if (this.loading) return;

				this.loading = true;

				try {
					const params = {
						status: this.currentStatus === '' ? '': this.currentStatus,
						type: 4, // 服务费
						pageNum: reset ? 1 : this.pageNum,
						pageSize: this.pageSize
					};

					const res = await this.$api.mine.walletList(params);

					if (res.code === '200') {
						const data = res.data;
						if (reset) {
							this.recordList = data.list || [];
							this.pageNum = 1;
						} else {
							this.recordList = [...this.recordList, ...(data.list || [])];
						}

						// 判断是否还有更多数据
						this.hasMore = this.pageNum < data.totalPage;
						if (!reset) {
							this.pageNum++;
						}
					} else {
						uni.showToast({
							title: res.msg || '获取数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取记录失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},

			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.getRecordList();
				}
			},

			// 切换状态筛选
			changeStatus(status) {
				if (this.currentStatus === status) return;

				this.currentStatus = status;
				this.pageNum = 1;
				this.hasMore = true;
				this.getRecordList(true);
			},

			// 处理记录点击 (Always navigate to detail)
			handleRecordTap(item) {
				this.viewDetail(item.id);
			},

			// 处理状态点击 (Handle specific actions)
			async handleStatusTap(item) {
				if (item.lock === 1) { // 待提现 或 已提现，未领取
					await this.processWithdrawal(item);
				} else if (item.lock === 0) { // 待审核
					await this.processCancel(item);
				} else {
					// If lock is not 0 or 1, clicking status also views detail
					this.viewDetail(item.id);
				}
			},

			// 查看详情
			viewDetail(id) {
				uni.navigateTo({
					url: `/user/walletDetail?id=${id}`
				});
			},

			// 处理提现请求
			async processWithdrawal(item) {
				uni.showModal({
					title: '确认提现',
					content: `确认提现 ￥${item.amount} 吗？`,
					confirmText: '确认',
					cancelText: '取消',
					success: async (res) => {
						if (res.confirm) {
							await this.executeWithdrawal(item);
						}
					}
				});
			},

			// 处理取消提现请求 (for '待审核' status)
			async processCancel(item) {
				uni.showModal({
					title: '取消提现',
					content: `确认取消提现申请吗？`,
					confirmText: '确认',
					cancelText: '取消',
					success: async (res) => {
						if (res.confirm) {
							await this.executeCancel(item);
						}
					}
				});
			},

			// 执行提现请求
			async executeWithdrawal(item) {
				uni.showLoading({ title: '提现中...' });

				try {
					const params = {
						id: item.id // 只传递提现记录id
					};

					const res = await this.$api.mine.withdrawalRequest(params);

					if (res.code === '200') {
						uni.showToast({
							title: '提现请求已提交',
							icon: 'success'
						});
							uni.requestMerchantTransfer({
								mchId: res.data.mchId,
								appId: res.data.appId,
								package: res.data.packageInfo,
								success: (transferRes) => {
									if (transferRes.result === 'success') {
										setTimeout(() => {
											this.refreshData();
										}, 1500);
									} else {
										uni.showToast({
											title: '提现申请失败，请稍后重试',
											icon: 'none',
										});
									}
								},
								fail: (transferRes) => {
									uni.showToast({
										title: transferRes.errMsg || '提现失败，请稍后重试',
										icon: 'none',
									});
								},
								complete: () => {
									this.isSubmitting = false;
								},
							});
						// 刷新列表数据

					} else {
						uni.showToast({
							title: res.msg || '提现请求失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('提现请求失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 执行取消提现请求
			async executeCancel(item) {
				uni.showLoading({ title: '取消中...' });
				try {
					const params = {
						id: item.id // Pass the withdrawal record ID
					};
					const res = await this.$api.mine.cancelStatSimple(item.id); // Assuming this is the correct API call
					if (res.code === '200') {
						uni.showToast({
							title: '提现申请已取消',
							icon: 'success'
						});
						this.refreshData(); // Refresh the list after successful cancellation
					} else {
						uni.showToast({
							title: res.msg || '取消提现失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('取消提现失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 获取状态样式类
			getStatusClass(item) {
				const statusMap = {
					'审核拒绝': 'status-rejected',
					'待提现': 'status-withdraw', // Will be overridden by lock for styling
					'已提现，未领取': 'status-withdraw', // Will be overridden by lock for styling
					'已提现': 'status-processing',
					'提现成功': 'status-success',
					'提现失败': 'status-failed',
					'已到账': 'status-success',
					'失败': 'status-failed',
					'关闭': 'status-closed',
					'待审核': 'status-pending' // Will be overridden by lock for styling
				};

				// Apply styling based on 'lock' field
				if (item.lock === 1) {
					return 'status-actionable'; // New class for actionable items (待提现, 已提现，未领取)
				} else if (item.lock === 0) {
					return 'status-pending-action'; // New class for '待审核' when user can cancel
				}

				return statusMap[item.statusText] || 'status-default';
			}
		}
	}
</script>

<style scoped lang="scss">
.page {
	background-color: #f0f2f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.header-stats {
	background: linear-gradient(135deg, #2e80fe 0%, #1e6bff 100%);
	padding: 60rpx 30rpx 80rpx;
	border-bottom-left-radius: 40rpx;
	border-bottom-right-radius: 40rpx;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);

	.stats-card {
		text-align: center;
		color: #ffffff;

		.stats-title {
			font-size: 30rpx;
			opacity: 0.95;
			margin-bottom: 24rpx;
			font-weight: 400;
		}

		.stats-amount {
			font-size: 56rpx;
			font-weight: bold;
			letter-spacing: 1rpx;
		}
	}
}

.filter-section {
	background: #ffffff;
	margin: -40rpx 30rpx 30rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	padding: 24rpx 0;
	/* Remove position: relative and overflow: hidden if u-scroll-list handles its own clipping/gradients */

	/* If using u-scroll-list, the .filter-tabs-container and its scrollbar hiding are handled by u-scroll-list internally.
	   You might still need to style the inner items. */

	/* Remove these custom gradient overlays if using a dedicated ScrollList component */
	/*
	&::before,
	&::after {
		content: '';
		position: absolute;
		top: 0;
		bottom: 0;
		width: 60rpx;
		pointer-events: none;
		z-index: 1;
	}

	&::before {
		left: 0;
		background: linear-gradient(to right, #ffffff 30%, rgba(255, 255, 255, 0) 100%);
	}

	&::after {
		right: 0;
		background: linear-gradient(to left, #ffffff 30%, rgba(255, 255, 255, 0) 100%);
	}
	*/


	/* Styling for the individual tabs within the scroll list */
	.filter-tab {
		padding: 18rpx 30rpx;
		font-size: 28rpx;
		color: #666666;
		border-radius: 24rpx;
		transition: all 0.3s ease;
		text-align: center;
		flex-shrink: 0; /* Prevent tabs from shrinking */
		margin: 0 10rpx; /* Add margin between tabs for better spacing, adjust as needed */

		&:first-child {
			margin-left: 30rpx; /* Align first tab with filter-section padding */
		}
		&:last-child {
			margin-right: 30rpx; /* Align last tab with filter-section padding */
		}

		&.active {
			color: #2e80fe;
			background: rgba(46, 128, 254, 0.15);
			font-weight: 600;
			box-shadow: 0 2rpx 8rpx rgba(46, 128, 254, 0.2);
		}
	}
	// The u-scroll-list component itself might have padding, so you may need to adjust the padding of .filter-section
	// or the margin of .filter-tab to ensure proper alignment and prevent double padding.
}

.record-list {
	padding: 0 30rpx;
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	align-items: center; /* Centers each record-item horizontally */

	.record-item {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx 40rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.07);
		transition: transform 0.2s ease-in-out;
		width: 100%; /* Take full width within padding */
		max-width: 700rpx; /* Optional: Constrain max width for large screens */

		&:active {
			transform: translateY(2rpx);
		}

		.record-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;

			.record-amount {
				font-size: 38rpx;
				font-weight: bold;
				color: #333333;
			}

			.record-status {
				padding: 10rpx 20rpx;
				border-radius: 16rpx;
				font-size: 26rpx;
				position: relative;
				cursor: pointer;
				display: flex;
				align-items: center;
				white-space: nowrap;

				&.status-rejected {
					background: #fde0e0;
					color: #d32f2f;
				}

				&.status-pending {
					background: #fff8e1;
					color: #f57c00;
				}

				&.status-actionable {
					background: #e1f5fe;
					color: #039be5;
					&:active {
						background: #bbdefb;
					}
					.tap-hint {
						font-size: 22rpx;
						margin-left: 10rpx;
						opacity: 0.85;
						color: inherit;
					}
				}

				&.status-pending-action {
					background: #fff8e1;
					color: #f57c00;
					&:active {
						background: #ffe0b2;
					}
					.tap-hint {
						font-size: 22rpx;
						margin-left: 10rpx;
						opacity: 0.85;
						color: inherit;
					}
				}

				&.status-processing {
					background: #e3f2fd;
					color: #2196f3;
				}

				&.status-success {
					background: #e8f5e8;
					color: #4caf50;
				}

				&.status-failed {
					background: #ffebee;
					color: #f44336;
				}

				&.status-closed {
					background: #f5f5f5;
					color: #999999;
				}

				&.status-default {
					background: #f0f0f0;
					color: #666666;
				}
			}
		}

		.record-info {
			.record-time {
				font-size: 26rpx;
				color: #888888;
				margin-bottom: 0;
			}
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 150rpx 0;
	flex-grow: 1;

	.empty-image {
		width: 240rpx;
		height: 240rpx;
		margin-bottom: 40rpx;
		opacity: 0.7;
	}

	.empty-text {
		font-size: 30rpx;
		color: #999999;
		font-weight: 500;
		margin-bottom: 400rpx;
	}
}

.load-more {
	padding: 40rpx 0 60rpx;
	text-align: center;

	.load-text {
		font-size: 28rpx;
		color: #999999;
	}
}
</style>
```
# 提现功能修复说明

## 问题描述
遇到错误：`TypeError: uni.requestMerchantTransfer is not a function`

## 问题原因
`uni.requestMerchantTransfer` 是一个 **uni ext api**，不是 uni-app 的内置 API，需要单独安装插件。

## 解决方案

### 方案一：安装 uni-requestMerchantTransfer 插件（推荐）

1. **下载插件**
   - 访问插件市场：https://ext.dcloud.net.cn/plugin?id=22283
   - 下载 `uni-requestMerchantTransfer` 插件
   - 导入到您的项目中

2. **配置要求**
   - App 平台：需要 HBuilderX 4.61+
   - 微信小程序：需要基础库 3.3.0+
   - 需要在微信支付商户平台申请开通商家转账功能

3. **使用方法**
   ```javascript
   uni.requestMerchantTransfer({
       mchId: '商户号',
       appId: '应用ID', 
       package: '付款单信息',
       success: (res) => {
           console.log('转账成功', res);
       },
       fail: (err) => {
           console.log('转账失败', err);
       }
   });
   ```

### 方案二：使用传统支付方式（已实现）

代码已经修改为兼容模式：
- 优先使用 `uni.requestMerchantTransfer`（如果插件已安装）
- 如果插件未安装，自动降级使用传统的 `uni.requestPayment`

## 代码修改说明

已修改 `user/coreWallet.vue` 文件的 `executeWithdrawal` 方法：

1. **添加 API 检查**：检查 `uni.requestMerchantTransfer` 是否可用
2. **兼容处理**：如果不可用，使用传统支付方式
3. **错误处理**：增加了更好的错误提示

## 推荐操作步骤

1. **立即可用**：当前代码已经可以正常运行，会使用传统支付方式
2. **长期方案**：建议安装 `uni-requestMerchantTransfer` 插件以获得更好的用户体验
3. **测试验证**：在各个平台测试提现功能是否正常

## 注意事项

1. **商户配置**：确保微信支付商户平台已开通商家转账功能
2. **参数配置**：确保服务端返回正确的支付参数
3. **平台兼容**：不同平台可能需要不同的参数格式

## 相关文档

- [uni.requestMerchantTransfer 官方文档](https://uniapp.dcloud.net.cn/api/plugins/request-merchant-transfer.html)
- [微信商家转账文档](https://pay.weixin.qq.com/doc/v3/merchant/4012711988)
- [uni-app 支付文档](https://uniapp.dcloud.net.cn/api/plugins/payment.html)
